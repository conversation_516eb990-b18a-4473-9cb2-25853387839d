<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\ChatController;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

// Main chat page
Route::get('/', [ChatController::class, 'index'])->name('chat.index');

// Chat API routes
Route::get('/messages', [ChatController::class, 'getMessages'])->name('chat.messages');
Route::post('/messages', [ChatController::class, 'sendMessage'])->name('chat.send');
Route::get('/user-info', [ChatController::class, 'getUserInfo'])->name('chat.user-info');
Route::delete('/messages', [ChatController::class, 'clearMessages'])->name('chat.clear');
