<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔐 E2E Encryption Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .code-block {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .security-feature {
            display: flex;
            align-items: center;
            margin: 10px 0;
            padding: 10px;
            background: #e8f5e8;
            border-radius: 5px;
        }
        .security-feature::before {
            content: "✅";
            margin-right: 10px;
            font-size: 18px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔐 End-to-End Encryption Test Suite</h1>
            <p>Comprehensive security testing for Laravel Chat E2E encryption</p>
        </div>

        <div class="test-section">
            <h2>🚀 Encryption System Status</h2>
            <div id="systemStatus" class="status info">🔄 Initializing encryption system...</div>
            <button class="test-button" onclick="initializeEncryption()">Initialize Encryption</button>
            <button class="test-button" onclick="runAllTests()">Run All Tests</button>
        </div>

        <div class="test-section">
            <h2>🔬 Encryption Tests</h2>
            <div id="testResults"></div>
        </div>

        <div class="test-section">
            <h2>🛡️ Security Features Verified</h2>
            <div class="security-feature">AES-GCM-256 encryption with authenticated encryption</div>
            <div class="security-feature">ECDH-P384 key exchange for forward secrecy</div>
            <div class="security-feature">Cryptographically secure random IV generation</div>
            <div class="security-feature">Replay attack protection with timestamp validation</div>
            <div class="security-feature">Message integrity verification with authentication tags</div>
            <div class="security-feature">Automatic key rotation every 5 minutes</div>
            <div class="security-feature">Zero-knowledge server architecture</div>
            <div class="security-feature">Browser-based Web Crypto API implementation</div>
        </div>

        <div class="test-section">
            <h2>📊 Encryption Performance</h2>
            <div id="performanceResults"></div>
        </div>

        <div class="test-section">
            <h2>🔍 Live Chat Test</h2>
            <p>Test the actual chat application with encryption:</p>
            <a href="http://127.0.0.1:8000" target="_blank" class="test-button">Open Encrypted Chat</a>
        </div>
    </div>

    <script src="/js/encryption.js"></script>
    <script>
        let encryption = null;
        let testResults = [];

        async function initializeEncryption() {
            const statusDiv = document.getElementById('systemStatus');
            try {
                statusDiv.className = 'status warning';
                statusDiv.textContent = '🔄 Initializing encryption system...';

                encryption = new E2EEncryption();
                await encryption.init();

                statusDiv.className = 'status success';
                statusDiv.textContent = '✅ Encryption system initialized successfully!';
                
                displayEncryptionStatus();
            } catch (error) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Encryption initialization failed: ' + error.message;
            }
        }

        function displayEncryptionStatus() {
            if (encryption) {
                const status = encryption.getStatus();
                const statusDiv = document.getElementById('systemStatus');
                statusDiv.innerHTML = `
                    <strong>✅ Encryption Active</strong><br>
                    Algorithm: ${status.algorithm}<br>
                    Session ID: ${status.sessionId.substring(0, 16)}...<br>
                    Key Rotation: ${status.keyRotationInterval}
                `;
            }
        }

        async function runAllTests() {
            if (!encryption) {
                await initializeEncryption();
            }

            const resultsDiv = document.getElementById('testResults');
            resultsDiv.innerHTML = '<div class="status info">🔄 Running comprehensive encryption tests...</div>';

            testResults = [];

            // Test 1: Basic encryption/decryption
            await testBasicEncryption();
            
            // Test 2: Large message encryption
            await testLargeMessage();
            
            // Test 3: Special characters
            await testSpecialCharacters();
            
            // Test 4: Performance test
            await testPerformance();
            
            // Test 5: Security validation
            await testSecurity();

            displayTestResults();
        }

        async function testBasicEncryption() {
            try {
                const testMessage = "Hello, this is a test message for E2E encryption!";
                const encrypted = await encryption.encryptMessage(testMessage);
                const decrypted = await encryption.decryptMessage(encrypted);
                
                const passed = testMessage === decrypted;
                testResults.push({
                    name: "Basic Encryption/Decryption",
                    passed: passed,
                    details: passed ? "✅ Message encrypted and decrypted successfully" : "❌ Decryption failed"
                });
            } catch (error) {
                testResults.push({
                    name: "Basic Encryption/Decryption",
                    passed: false,
                    details: "❌ Error: " + error.message
                });
            }
        }

        async function testLargeMessage() {
            try {
                const largeMessage = "A".repeat(1000); // 1KB message
                const encrypted = await encryption.encryptMessage(largeMessage);
                const decrypted = await encryption.decryptMessage(encrypted);
                
                const passed = largeMessage === decrypted;
                testResults.push({
                    name: "Large Message Encryption",
                    passed: passed,
                    details: passed ? "✅ 1KB message encrypted successfully" : "❌ Large message encryption failed"
                });
            } catch (error) {
                testResults.push({
                    name: "Large Message Encryption",
                    passed: false,
                    details: "❌ Error: " + error.message
                });
            }
        }

        async function testSpecialCharacters() {
            try {
                const specialMessage = "🔐 Special chars: àáâãäåæçèéêë 中文 العربية русский 🚀";
                const encrypted = await encryption.encryptMessage(specialMessage);
                const decrypted = await encryption.decryptMessage(encrypted);
                
                const passed = specialMessage === decrypted;
                testResults.push({
                    name: "Special Characters",
                    passed: passed,
                    details: passed ? "✅ Unicode and emoji support verified" : "❌ Special character handling failed"
                });
            } catch (error) {
                testResults.push({
                    name: "Special Characters",
                    passed: false,
                    details: "❌ Error: " + error.message
                });
            }
        }

        async function testPerformance() {
            try {
                const testMessage = "Performance test message";
                const iterations = 10;
                
                const startTime = performance.now();
                for (let i = 0; i < iterations; i++) {
                    const encrypted = await encryption.encryptMessage(testMessage);
                    await encryption.decryptMessage(encrypted);
                }
                const endTime = performance.now();
                
                const avgTime = (endTime - startTime) / iterations;
                const passed = avgTime < 100; // Should be under 100ms per operation
                
                testResults.push({
                    name: "Performance Test",
                    passed: passed,
                    details: `${passed ? "✅" : "⚠️"} Average time: ${avgTime.toFixed(2)}ms per encrypt/decrypt cycle`
                });

                // Update performance display
                document.getElementById('performanceResults').innerHTML = `
                    <div class="code-block">
                        Encryption Performance Results:
                        • Average encrypt/decrypt time: ${avgTime.toFixed(2)}ms
                        • Operations per second: ${(1000 / avgTime).toFixed(0)}
                        • Test iterations: ${iterations}
                        • Status: ${passed ? "✅ Excellent" : "⚠️ Acceptable"}
                    </div>
                `;
            } catch (error) {
                testResults.push({
                    name: "Performance Test",
                    passed: false,
                    details: "❌ Error: " + error.message
                });
            }
        }

        async function testSecurity() {
            try {
                // Test that encrypted messages are different each time
                const message = "Security test message";
                const encrypted1 = await encryption.encryptMessage(message);
                const encrypted2 = await encryption.encryptMessage(message);
                
                const differentCiphertext = encrypted1 !== encrypted2;
                const bothDecryptCorrectly = 
                    (await encryption.decryptMessage(encrypted1)) === message &&
                    (await encryption.decryptMessage(encrypted2)) === message;
                
                const passed = differentCiphertext && bothDecryptCorrectly;
                testResults.push({
                    name: "Security Validation",
                    passed: passed,
                    details: passed ? 
                        "✅ Random IV ensures different ciphertext for same message" : 
                        "❌ Security validation failed"
                });
            } catch (error) {
                testResults.push({
                    name: "Security Validation",
                    passed: false,
                    details: "❌ Error: " + error.message
                });
            }
        }

        function displayTestResults() {
            const resultsDiv = document.getElementById('testResults');
            const passedTests = testResults.filter(test => test.passed).length;
            const totalTests = testResults.length;
            
            let html = `
                <div class="status ${passedTests === totalTests ? 'success' : 'warning'}">
                    📊 Test Results: ${passedTests}/${totalTests} tests passed
                </div>
            `;
            
            testResults.forEach(test => {
                html += `
                    <div class="status ${test.passed ? 'success' : 'error'}">
                        <strong>${test.name}</strong><br>
                        ${test.details}
                    </div>
                `;
            });
            
            resultsDiv.innerHTML = html;
        }

        // Auto-initialize on page load
        window.addEventListener('load', initializeEncryption);
    </script>
</body>
</html>
