<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>👥 User Presence Test</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .status-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        .test-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
        }
        .user-list {
            background: #e8f5e8;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-family: monospace;
        }
        .instructions {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>👥 User Presence Test - FIXED!</h1>
            <p>Testing the fixed online user detection system</p>
        </div>

        <div class="success">
            <h3>✅ Issue Fixed!</h3>
            <p>The "No other users online" issue has been resolved. The user presence tracking system now works correctly.</p>
        </div>

        <div class="status-section">
            <h2>🔧 What Was Fixed</h2>
            <ul>
                <li><strong>User Presence Updates:</strong> Now updates every time messages are fetched (every 1 second)</li>
                <li><strong>Online Users Endpoint:</strong> Updates current user presence when fetching other users</li>
                <li><strong>Initial Load:</strong> Loads online users immediately when app starts</li>
                <li><strong>Debug Logging:</strong> Added console logs to help troubleshoot</li>
            </ul>
        </div>

        <div class="status-section">
            <h2>📊 Current Online Users</h2>
            <button class="test-button" onclick="checkOnlineUsers()">Check Online Users</button>
            <div id="usersList" class="user-list">
                Click "Check Online Users" to see current users
            </div>
        </div>

        <div class="instructions">
            <h3>🧪 How to Test Private Chat</h3>
            <ol>
                <li><strong>Open Multiple Windows:</strong> Open the chat app in 2-3 different browser windows/tabs</li>
                <li><strong>Each Gets Different Username:</strong> Each window will get a unique random username</li>
                <li><strong>Click "Private Chat" Tab:</strong> In any window, switch to private chat mode</li>
                <li><strong>See Other Users:</strong> You should now see the other users listed</li>
                <li><strong>Start Private Chat:</strong> Click on any user to start a private encrypted conversation</li>
                <li><strong>Send Messages:</strong> Messages will be encrypted with unique keys for that chat</li>
            </ol>
        </div>

        <div class="status-section">
            <h2>🚀 Test the Fixed Chat</h2>
            <div style="text-align: center;">
                <a href="http://127.0.0.1:8000" target="_blank" class="test-button" style="font-size: 18px; padding: 15px 30px;">
                    Open Chat App (Window 1)
                </a>
                <a href="http://127.0.0.1:8000" target="_blank" class="test-button" style="font-size: 18px; padding: 15px 30px;">
                    Open Chat App (Window 2)
                </a>
            </div>
            <p style="text-align: center; margin-top: 20px; color: #666; font-style: italic;">
                Open both windows, then click "Private Chat" in either window to see the other user!
            </p>
        </div>

        <div class="status-section">
            <h2>🔍 Debug Information</h2>
            <p>If you open the browser console (F12), you'll now see debug logs like:</p>
            <div class="user-list">
📡 Loaded online users: [{"username":"BraveUnicorn32","last_seen":1748443593,"status":"online"}]
🔄 Updating users list. Chat type: private Online users: 1
            </div>
        </div>

        <div class="status-section">
            <h2>✅ Expected Results</h2>
            <ul>
                <li>✅ Multiple users appear in the online users list</li>
                <li>✅ Users can click on each other to start private chats</li>
                <li>✅ Private messages are encrypted with individual keys</li>
                <li>✅ Real-time updates show new users joining</li>
                <li>✅ No more "No other users online" message (unless actually alone)</li>
            </ul>
        </div>
    </div>

    <script>
        async function checkOnlineUsers() {
            try {
                const response = await fetch('/online-users');
                const users = await response.json();
                
                const usersList = document.getElementById('usersList');
                
                if (users.length === 0) {
                    usersList.innerHTML = `
                        <div style="color: #856404;">
                            No other users currently online.<br>
                            Open another browser window to test!
                        </div>
                    `;
                } else {
                    let html = `<strong>Found ${users.length} online user(s):</strong><br><br>`;
                    users.forEach((user, index) => {
                        const lastSeenDate = new Date(user.last_seen * 1000);
                        html += `${index + 1}. ${user.username} (Last seen: ${lastSeenDate.toLocaleTimeString()})<br>`;
                    });
                    usersList.innerHTML = html;
                }
                
                console.log('Online users:', users);
            } catch (error) {
                console.error('Error checking online users:', error);
                document.getElementById('usersList').innerHTML = `
                    <div style="color: #dc3545;">
                        Error loading users: ${error.message}
                    </div>
                `;
            }
        }

        // Auto-check on page load
        window.addEventListener('load', () => {
            setTimeout(checkOnlineUsers, 1000);
        });

        // Auto-refresh every 5 seconds
        setInterval(checkOnlineUsers, 5000);

        console.log('👥 User Presence Test Suite Loaded');
        console.log('The "No other users online" issue has been fixed!');
    </script>
</body>
</html>
