<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ChatController extends Controller
{
    /**
     * Display the main chat page
     */
    public function index()
    {
        // Generate a random username if not already set
        if (!Session::has('chat_username')) {
            $username = $this->generateRandomUsername();
            Session::put('chat_username', $username);
        }

        $username = Session::get('chat_username');

        return view('chat.index', compact('username'));
    }

    /**
     * Get all messages
     */
    public function getMessages()
    {
        $messages = Cache::get('chat_messages', []);

        // Keep only the last 100 messages to prevent memory issues
        if (count($messages) > 100) {
            $messages = array_slice($messages, -100);
            Cache::put('chat_messages', $messages, 3600); // Cache for 1 hour
        }

        return response()->json($messages);
    }

    /**
     * Send a new message (now handles encrypted content)
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:2000', // Increased limit for encrypted data
            'encrypted' => 'boolean'
        ]);

        $username = Session::get('chat_username');
        if (!$username) {
            return response()->json(['error' => 'No username found'], 400);
        }

        // Server never sees the plaintext - only encrypted data
        $message = [
            'id' => Str::uuid(),
            'username' => $username,
            'message' => $request->message, // This is encrypted on client side
            'encrypted' => $request->input('encrypted', false),
            'timestamp' => now()->toISOString(),
            'formatted_time' => now()->format('H:i'),
            'server_hash' => hash('sha256', $request->message . $username . now()->timestamp) // Integrity check
        ];

        // Get existing messages
        $messages = Cache::get('chat_messages', []);

        // Add new message
        $messages[] = $message;

        // Keep only the last 100 messages
        if (count($messages) > 100) {
            $messages = array_slice($messages, -100);
        }

        // Store back in cache
        Cache::put('chat_messages', $messages, 3600); // Cache for 1 hour

        return response()->json($message);
    }

    /**
     * Get current user info
     */
    public function getUserInfo()
    {
        $username = Session::get('chat_username');

        if (!$username) {
            $username = $this->generateRandomUsername();
            Session::put('chat_username', $username);
        }

        return response()->json(['username' => $username]);
    }

    /**
     * Generate a random username
     */
    private function generateRandomUsername()
    {
        $adjectives = [
            'Happy', 'Clever', 'Bright', 'Swift', 'Brave', 'Kind', 'Wise', 'Cool',
            'Smart', 'Quick', 'Bold', 'Calm', 'Eager', 'Fair', 'Gentle', 'Jolly',
            'Keen', 'Lively', 'Merry', 'Noble', 'Proud', 'Sharp', 'Witty', 'Zesty'
        ];

        $animals = [
            'Lion', 'Tiger', 'Eagle', 'Wolf', 'Bear', 'Fox', 'Owl', 'Hawk',
            'Dolphin', 'Shark', 'Falcon', 'Panther', 'Jaguar', 'Cheetah', 'Lynx',
            'Raven', 'Phoenix', 'Dragon', 'Unicorn', 'Griffin', 'Pegasus', 'Sphinx'
        ];

        $adjective = $adjectives[array_rand($adjectives)];
        $animal = $animals[array_rand($animals)];
        $number = rand(10, 99);

        return $adjective . $animal . $number;
    }

    /**
     * Clear all messages (for testing purposes)
     */
    public function clearMessages()
    {
        Cache::forget('chat_messages');
        return response()->json(['message' => 'All messages cleared']);
    }
}
