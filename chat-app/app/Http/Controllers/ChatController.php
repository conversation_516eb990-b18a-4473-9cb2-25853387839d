<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Session;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class ChatController extends Controller
{
    /**
     * Display the main chat page
     */
    public function index()
    {
        // Generate a random username if not already set
        if (!Session::has('chat_username')) {
            $username = $this->generateRandomUsername();
            Session::put('chat_username', $username);
        }

        $username = Session::get('chat_username');

        return view('chat.index', compact('username'));
    }

    /**
     * Get all messages (public or private chat)
     */
    public function getMessages(Request $request)
    {
        $chatType = $request->input('chat_type', 'public');
        $chatWith = $request->input('chat_with');

        // Update user presence every time messages are fetched
        $currentUser = Session::get('chat_username');
        if ($currentUser) {
            $this->updateUserPresence($currentUser);
        }

        if ($chatType === 'private' && $chatWith) {
            // Get private chat messages
            $chatRoomId = $this->generateChatRoomId($currentUser, $chatWith);
            $messages = Cache::get("private_chat_{$chatRoomId}", []);
        } else {
            // Get public chat messages
            $messages = Cache::get('chat_messages', []);
        }

        // Keep only the last 100 messages to prevent memory issues
        if (count($messages) > 100) {
            $messages = array_slice($messages, -100);
            if ($chatType === 'private' && $chatWith) {
                $currentUser = Session::get('chat_username');
                $chatRoomId = $this->generateChatRoomId($currentUser, $chatWith);
                Cache::put("private_chat_{$chatRoomId}", $messages, 3600);
            } else {
                Cache::put('chat_messages', $messages, 3600);
            }
        }

        return response()->json($messages);
    }

    /**
     * Send a new message (now handles encrypted content and private chats)
     */
    public function sendMessage(Request $request)
    {
        $request->validate([
            'message' => 'required|string|max:2000', // Increased limit for encrypted data
            'encrypted' => 'boolean',
            'chat_type' => 'string|in:public,private',
            'chat_with' => 'string|nullable'
        ]);

        $username = Session::get('chat_username');
        if (!$username) {
            return response()->json(['error' => 'No username found'], 400);
        }

        $chatType = $request->input('chat_type', 'public');
        $chatWith = $request->input('chat_with');

        // Server never sees the plaintext - only encrypted data
        $message = [
            'id' => Str::uuid(),
            'username' => $username,
            'message' => $request->message, // This is encrypted on client side
            'encrypted' => $request->input('encrypted', false),
            'chat_type' => $chatType,
            'chat_with' => $chatWith,
            'timestamp' => now()->toISOString(),
            'formatted_time' => now()->format('H:i'),
            'server_hash' => hash('sha256', $request->message . $username . now()->timestamp) // Integrity check
        ];

        if ($chatType === 'private' && $chatWith) {
            // Store in private chat
            $chatRoomId = $this->generateChatRoomId($username, $chatWith);
            $messages = Cache::get("private_chat_{$chatRoomId}", []);
        } else {
            // Store in public chat
            $messages = Cache::get('chat_messages', []);
        }

        // Add new message
        $messages[] = $message;

        // Keep only the last 100 messages
        if (count($messages) > 100) {
            $messages = array_slice($messages, -100);
        }

        // Store back in cache
        if ($chatType === 'private' && $chatWith) {
            Cache::put("private_chat_{$chatRoomId}", $messages, 3600);
        } else {
            Cache::put('chat_messages', $messages, 3600);
        }

        return response()->json($message);
    }

    /**
     * Get current user info and update presence
     */
    public function getUserInfo()
    {
        $username = Session::get('chat_username');

        if (!$username) {
            $username = $this->generateRandomUsername();
            Session::put('chat_username', $username);
        }

        // Update user presence
        $this->updateUserPresence($username);

        return response()->json(['username' => $username]);
    }

    /**
     * Get list of online users
     */
    public function getOnlineUsers()
    {
        $currentUser = Session::get('chat_username');

        // Generate username if not exists
        if (!$currentUser) {
            $currentUser = $this->generateRandomUsername();
            Session::put('chat_username', $currentUser);
        }

        // Update current user's presence first
        $this->updateUserPresence($currentUser);

        $onlineUsers = Cache::get('online_users', []);

        // Remove expired users (inactive for more than 2 minutes)
        $activeUsers = [];
        $currentTime = now()->timestamp;

        foreach ($onlineUsers as $username => $lastSeen) {
            if ($currentTime - $lastSeen < 120) { // 2 minutes
                $activeUsers[$username] = $lastSeen;
            }
        }

        // Update cache with active users only
        Cache::put('online_users', $activeUsers, 3600);

        // Remove current user from the list (don't show yourself)
        unset($activeUsers[$currentUser]);

        // Convert to array of usernames with last seen info
        $userList = [];
        foreach ($activeUsers as $username => $lastSeen) {
            $userList[] = [
                'username' => $username,
                'last_seen' => $lastSeen,
                'status' => 'online'
            ];
        }

        return response()->json($userList);
    }

    /**
     * Update user presence
     */
    private function updateUserPresence($username)
    {
        $onlineUsers = Cache::get('online_users', []);
        $onlineUsers[$username] = now()->timestamp;
        Cache::put('online_users', $onlineUsers, 3600);
    }

    /**
     * Generate a random username
     */
    private function generateRandomUsername()
    {
        $adjectives = [
            'Happy', 'Clever', 'Bright', 'Swift', 'Brave', 'Kind', 'Wise', 'Cool',
            'Smart', 'Quick', 'Bold', 'Calm', 'Eager', 'Fair', 'Gentle', 'Jolly',
            'Keen', 'Lively', 'Merry', 'Noble', 'Proud', 'Sharp', 'Witty', 'Zesty'
        ];

        $animals = [
            'Lion', 'Tiger', 'Eagle', 'Wolf', 'Bear', 'Fox', 'Owl', 'Hawk',
            'Dolphin', 'Shark', 'Falcon', 'Panther', 'Jaguar', 'Cheetah', 'Lynx',
            'Raven', 'Phoenix', 'Dragon', 'Unicorn', 'Griffin', 'Pegasus', 'Sphinx'
        ];

        $adjective = $adjectives[array_rand($adjectives)];
        $animal = $animals[array_rand($animals)];
        $number = rand(10, 99);

        return $adjective . $animal . $number;
    }

    /**
     * Generate a consistent chat room ID for two users
     */
    private function generateChatRoomId($user1, $user2)
    {
        // Sort usernames to ensure consistent room ID regardless of order
        $users = [$user1, $user2];
        sort($users);
        return hash('sha256', implode('_', $users));
    }

    /**
     * Clear all messages (for testing purposes)
     */
    public function clearMessages()
    {
        Cache::forget('chat_messages');
        return response()->json(['message' => 'All messages cleared']);
    }
}
