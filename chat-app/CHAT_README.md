# 🔐 Laravel Chat Application (End-to-End Encrypted)

A secure, real-time chat application built with <PERSON><PERSON> featuring **100% end-to-end encryption**. No registration required, no database connections, and military-grade security. Every visitor gets an automatically assigned random username and can start chatting immediately with complete privacy.

## Features

- **No Registration Required**: Just open the app and start chatting
- **Automatic Random Usernames**: Each visitor gets a unique, randomly generated username (e.g., "BraveUnicorn32", "CleverTiger45")
- **Real-time Messaging**: Messages appear in real-time for all users with no UI flashing
- **No Database**: Uses <PERSON>vel's cache system to store messages temporarily
- **Responsive Design**: Works on desktop and mobile devices
- **Character Counter**: Shows remaining characters while typing (500 character limit)
- **Clean UI**: Modern, gradient-based design with smooth animations
- **Flicker-Free Updates**: Smart message loading prevents UI flashing during updates
- **Instant Message Display**: Your sent messages appear immediately without waiting
- **🔐 End-to-End Encryption**: Military-grade AES-GCM-256 encryption
- **🛡️ Zero-Knowledge Server**: Server never sees your messages
- **🔄 Forward Secrecy**: Automatic key rotation every 5 minutes
- **🚫 No Data Persistence**: Messages and keys are ephemeral
- **👥 Private 1-on-1 Chat**: Secure private messaging between specific users
- **🔑 Individual Encryption Keys**: Each private chat has unique encryption keys
- **📱 Dual Chat Interface**: Switch between public and private chats seamlessly
- **👁️ Real-time User Presence**: See who's online and available for private chat

## How It Works

1. **Visit the Application**: Open your browser and go to the chat application URL
2. **Automatic Username**: You'll be automatically assigned a random username like "SwiftEagle23"
3. **Start Chatting**: Type your message and press Enter or click Send
4. **Real-time Updates**: Messages from other users appear automatically every second
5. **Session-based**: Your username persists for your browser session

## Technical Details

### Architecture
- **Frontend**: HTML, CSS, JavaScript (vanilla)
- **Backend**: Laravel 8 with session-based user management
- **Storage**: Laravel Cache (file-based by default, can be configured for Redis)
- **Real-time**: Polling-based updates every 1 second
- **No Database**: All data stored in cache/memory

### Username Generation
Random usernames are generated using:
- 24 adjectives (Happy, Clever, Bright, Swift, etc.)
- 22 animals (Lion, Tiger, Eagle, Wolf, etc.)
- Random 2-digit number (10-99)
- Format: `[Adjective][Animal][Number]` (e.g., "BraveUnicorn32")

### Message Storage
- Messages are stored in Laravel's cache system
- Maximum of 100 messages kept in memory
- Messages expire after 1 hour
- Each message includes: ID, username, content, timestamp

### No-Flash Real-time Updates
The chat implements a sophisticated no-flash system:
- **Message ID Tracking**: Each message has a unique ID tracked in a JavaScript Set
- **Incremental DOM Updates**: Only new messages are appended, existing ones stay in place
- **No DOM Clearing**: The messages container is never cleared and rebuilt
- **Instant Own Messages**: Your sent messages appear immediately without waiting for polls
- **Smart Polling**: Polls every 1 second but only processes truly new messages
- **Smooth Animations**: Different animations for own messages (instant) vs others (slide-in)
- **Smooth Scrolling**: Auto-scroll uses smooth behavior instead of instant jumps

## Installation & Setup

1. **Clone/Download** the Laravel application
2. **Install Dependencies**:
   ```bash
   composer install
   ```
3. **Environment Setup**:
   ```bash
   cp .env.example .env
   php artisan key:generate
   ```
4. **Start the Server**:
   ```bash
   php artisan serve
   ```
5. **Open Browser**: Visit `http://127.0.0.1:8000`

## API Endpoints

- `GET /` - Main chat interface
- `GET /messages` - Retrieve all messages (JSON)
- `POST /messages` - Send a new message
- `GET /user-info` - Get current user's username
- `DELETE /messages` - Clear all messages (for testing)

## Configuration

### Cache Configuration
By default, the app uses file-based caching. For better performance with multiple users, configure Redis:

1. Install Redis
2. Update `.env`:
   ```
   CACHE_DRIVER=redis
   REDIS_HOST=127.0.0.1
   REDIS_PASSWORD=null
   REDIS_PORT=6379
   ```

### Session Configuration
Sessions are file-based by default. For production, consider:
- Redis sessions for better performance
- Database sessions for persistence

## Customization

### Username Generation
Edit `ChatController.php` method `generateRandomUsername()` to:
- Add more adjectives/animals
- Change the format
- Add different naming schemes

### Message Limits
- Character limit: Change `maxlength` in the input field and validation rules
- Message history: Modify the 100-message limit in `ChatController.php`
- Cache duration: Adjust the 3600 seconds (1 hour) cache time

### Styling
All styles are in the main view file. Customize:
- Colors and gradients
- Fonts and typography
- Layout and spacing
- Animations and transitions

## Testing Multiple Users

To test with multiple users:
1. Open multiple browser windows/tabs
2. Use different browsers
3. Use incognito/private browsing mode
4. Each will get a different random username

## Production Considerations

1. **Performance**: Use Redis for caching and sessions
2. **Security**: Add rate limiting for message sending
3. **Moderation**: Add content filtering/moderation
4. **Scaling**: Consider WebSocket solutions for larger user bases
5. **Monitoring**: Add logging and error tracking

## Troubleshooting

- **Messages not appearing**: Check if cache is working and server is running
- **Username not generating**: Verify session configuration
- **CSRF errors**: Ensure meta tag is present and CSRF token is valid
- **Performance issues**: Consider Redis for caching

## Browser Compatibility

- Chrome/Chromium (recommended)
- Firefox
- Safari
- Edge
- Mobile browsers (iOS Safari, Chrome Mobile)

## License

This chat application is open-source and free to use and modify.
