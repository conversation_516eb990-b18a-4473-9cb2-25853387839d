<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title><PERSON><PERSON>t App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 95%;
            max-width: 1200px;
            height: 90vh;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            overflow: hidden;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e0e0e0;
            display: flex;
            flex-direction: column;
        }

        .chat-main {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .user-info {
            font-size: 14px;
            opacity: 0.9;
        }

        .online-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            opacity: 0;
            animation: slideIn 0.4s ease-out forwards;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.instant {
            animation: fadeIn 0.2s ease-out forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .message.own {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.own .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.other .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .message-info {
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.7;
        }

        .message.own .message-info {
            text-align: right;
        }

        .message.other .message-info {
            text-align: left;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            padding: 10px 20px;
            font-style: italic;
            color: #666;
            font-size: 14px;
        }

        .no-messages {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 50px;
        }

        .sidebar-header {
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-align: center;
        }

        .sidebar-header h3 {
            margin: 0;
            font-size: 18px;
        }

        .chat-tabs {
            display: flex;
            background: #e9ecef;
            border-bottom: 1px solid #dee2e6;
        }

        .chat-tab {
            flex: 1;
            padding: 12px;
            text-align: center;
            cursor: pointer;
            border: none;
            background: transparent;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s;
        }

        .chat-tab.active {
            background: white;
            color: #667eea;
            border-bottom: 2px solid #667eea;
        }

        .chat-tab:hover {
            background: #f8f9fa;
        }

        .users-list {
            flex: 1;
            overflow-y: auto;
            padding: 10px;
        }

        .user-item {
            display: flex;
            align-items: center;
            padding: 12px;
            margin: 5px 0;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s;
            border: 1px solid transparent;
        }

        .user-item:hover {
            background: #e9ecef;
            border-color: #667eea;
        }

        .user-item.active {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .user-avatar {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            margin-right: 12px;
        }

        .user-info {
            flex: 1;
        }

        .user-name {
            font-weight: 500;
            font-size: 14px;
        }

        .user-status {
            font-size: 12px;
            opacity: 0.7;
        }

        .online-dot {
            width: 8px;
            height: 8px;
            background: #4CAF50;
            border-radius: 50%;
            margin-left: 8px;
        }

        .chat-title {
            padding: 15px 20px;
            background: #f8f9fa;
            border-bottom: 1px solid #e0e0e0;
            font-weight: 500;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .chat-type-indicator {
            font-size: 12px;
            padding: 4px 8px;
            border-radius: 12px;
            background: #667eea;
            color: white;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 10px;
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
                height: 200px;
                border-right: none;
                border-bottom: 1px solid #e0e0e0;
            }

            .message-bubble {
                max-width: 85%;
            }

            .chat-header h1 {
                font-size: 20px;
            }
        }

        /* Scrollbar styling */
        .messages-container::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <!-- Sidebar with users list -->
        <div class="sidebar">
            <div class="sidebar-header">
                <h3>🔐 Secure Chat</h3>
                <div style="font-size: 12px; margin-top: 5px;">{{ $username }}</div>
            </div>

            <div class="chat-tabs">
                <button class="chat-tab active" id="publicTab" onclick="switchToPublicChat()">
                    🌐 Public Chat
                </button>
                <button class="chat-tab" id="privateTab" onclick="showUsersList()">
                    👥 Private Chat
                </button>
            </div>

            <div class="users-list" id="usersList">
                <div style="text-align: center; padding: 20px; color: #666; font-style: italic;">
                    Click "Private Chat" to see online users
                </div>
            </div>
        </div>

        <!-- Main chat area -->
        <div class="chat-main">
            <div class="chat-title" id="chatTitle">
                <span>🌐 Public Chat</span>
                <span class="chat-type-indicator" id="chatTypeIndicator">Public</span>
            </div>

            <div class="chat-header" style="padding: 15px 20px;">
                <div class="online-indicator"></div>
                <div class="encryption-status" id="encryptionStatus" style="font-size: 12px; opacity: 0.8;">
                    🔄 Initializing encryption...
                </div>
            </div>

            <div class="messages-container" id="messagesContainer">
                <div class="no-messages" id="noMessages">
                    No messages yet. Start the conversation!
                </div>
            </div>

            <div class="input-container">
                <input
                    type="text"
                    class="message-input"
                    id="messageInput"
                    placeholder="Type your message..."
                    maxlength="500"
                >
                <button class="send-button" id="sendButton">Send</button>
            </div>
            <div class="character-count" id="characterCount" style="padding: 5px 20px; font-size: 12px; color: #666; text-align: right;">
                500 characters remaining
            </div>
        </div>
    </div>

    <!-- Load encryption library -->
    <script src="/js/encryption.js"></script>
    <script>
        // Chat application JavaScript
        class ChatApp {
            constructor() {
                this.messagesContainer = document.getElementById('messagesContainer');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.noMessages = document.getElementById('noMessages');
                this.characterCount = document.getElementById('characterCount');
                this.encryptionStatus = document.getElementById('encryptionStatus');
                this.usersList = document.getElementById('usersList');
                this.chatTitle = document.getElementById('chatTitle');
                this.chatTypeIndicator = document.getElementById('chatTypeIndicator');
                this.currentUsername = '{{ $username }}';
                this.lastMessageId = null;
                this.displayedMessages = new Set();
                this.encryption = null;
                this.encryptionReady = false;
                this.currentChatType = 'public';
                this.currentChatWith = null;
                this.onlineUsers = [];

                // Make username available globally for encryption
                window.currentUsername = this.currentUsername;

                this.init();
            }

            async init() {
                // Set up CSRF token for AJAX requests
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Initialize encryption first
                await this.initializeEncryption();

                // Set up event listeners
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });
                this.messageInput.addEventListener('input', () => this.updateCharacterCount());

                // Load initial messages
                this.loadMessages();

                // Poll for new messages every 1 second for better real-time experience
                setInterval(() => this.loadMessages(), 1000);

                // Poll for online users every 3 seconds
                setInterval(() => this.loadOnlineUsers(), 3000);

                // Initial character count update
                this.updateCharacterCount();
            }

            async initializeEncryption() {
                try {
                    this.updateEncryptionStatus('🔄 Initializing encryption...', '#ffa500');

                    this.encryption = new E2EEncryption();
                    await this.encryption.init();

                    // Test encryption
                    const testResult = await this.encryption.testEncryption();
                    if (testResult) {
                        this.encryptionReady = true;
                        this.updateEncryptionStatus('🔐 End-to-end encryption active', '#4CAF50');
                        console.log('✅ Encryption initialized successfully');
                    } else {
                        throw new Error('Encryption test failed');
                    }
                } catch (error) {
                    console.error('❌ Encryption initialization failed:', error);
                    this.updateEncryptionStatus('⚠️ Encryption failed - messages not secure', '#ff6b6b');
                    this.encryptionReady = false;
                }
            }

            updateEncryptionStatus(message, color = '#666') {
                if (this.encryptionStatus) {
                    this.encryptionStatus.textContent = message;
                    this.encryptionStatus.style.color = color;
                }
            }

            async loadMessages() {
                try {
                    const url = new URL('/messages', window.location.origin);
                    url.searchParams.append('chat_type', this.currentChatType);
                    if (this.currentChatWith) {
                        url.searchParams.append('chat_with', this.currentChatWith);
                    }

                    const response = await fetch(url);
                    const messages = await response.json();
                    this.processMessages(messages);
                } catch (error) {
                    console.error('Error loading messages:', error);
                }
            }

            async loadOnlineUsers() {
                try {
                    const response = await fetch('/online-users');
                    const users = await response.json();
                    this.onlineUsers = users;
                    this.updateUsersList();
                } catch (error) {
                    console.error('Error loading online users:', error);
                }
            }

            async processMessages(messages) {
                if (messages.length === 0) {
                    this.noMessages.style.display = 'block';
                    return;
                }

                this.noMessages.style.display = 'none';

                // If this is the first load, display all messages
                if (this.displayedMessages.size === 0) {
                    for (const message of messages) {
                        await this.addMessageToDOM(message);
                        this.displayedMessages.add(message.id);
                    }
                    this.scrollToBottom();
                    return;
                }

                // Only add new messages that haven't been displayed yet
                let hasNewMessages = false;
                for (const message of messages) {
                    if (!this.displayedMessages.has(message.id)) {
                        await this.addMessageToDOM(message);
                        this.displayedMessages.add(message.id);
                        hasNewMessages = true;
                    }
                }

                // Only scroll to bottom if there are new messages
                if (hasNewMessages) {
                    this.scrollToBottom();
                }
            }

            async addMessageToDOM(message, isInstant = false) {
                const messageDiv = document.createElement('div');
                const isOwnMessage = message.username === this.currentUsername;
                messageDiv.className = `message ${isOwnMessage ? 'own' : 'other'}${isInstant ? ' instant' : ''}`;

                // Decrypt message if it's encrypted and encryption is ready
                let displayMessage = message.message;
                let encryptionIcon = '';

                if (message.encrypted && this.encryptionReady && this.encryption) {
                    try {
                        displayMessage = await this.encryption.decryptMessage(
                            message.message,
                            message.chat_type || this.currentChatType,
                            message.chat_with || this.currentChatWith
                        );
                        encryptionIcon = '🔐 ';
                    } catch (error) {
                        console.error('Failed to decrypt message:', error);
                        displayMessage = '[🔒 Encrypted message - decryption failed]';
                        encryptionIcon = '⚠️ ';
                    }
                } else if (message.encrypted) {
                    displayMessage = '[🔒 Encrypted message - encryption not ready]';
                    encryptionIcon = '⚠️ ';
                }

                messageDiv.innerHTML = `
                    <div class="message-info">
                        ${encryptionIcon}${isOwnMessage ? 'You' : message.username} • ${message.formatted_time}
                    </div>
                    <div class="message-bubble">
                        ${this.escapeHtml(displayMessage)}
                    </div>
                `;

                this.messagesContainer.appendChild(messageDiv);
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Disable send button
                this.sendButton.disabled = true;
                this.sendButton.textContent = 'Encrypting...';

                try {
                    let messageToSend = message;
                    let isEncrypted = false;

                    // Encrypt message if encryption is ready
                    if (this.encryptionReady && this.encryption) {
                        try {
                            messageToSend = await this.encryption.encryptMessage(message, this.currentChatType, this.currentChatWith);
                            isEncrypted = true;
                            this.sendButton.textContent = 'Sending...';
                        } catch (error) {
                            console.error('Encryption failed:', error);
                            this.updateEncryptionStatus('⚠️ Encryption failed - sending unencrypted', '#ff6b6b');
                            // Fall back to unencrypted
                            messageToSend = message;
                            isEncrypted = false;
                        }
                    } else {
                        console.warn('Encryption not ready - sending unencrypted message');
                        this.updateEncryptionStatus('⚠️ Sending unencrypted message', '#ffa500');
                    }

                    const response = await fetch('/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            message: messageToSend,
                            encrypted: isEncrypted,
                            chat_type: this.currentChatType,
                            chat_with: this.currentChatWith
                        })
                    });

                    if (response.ok) {
                        const sentMessage = await response.json();
                        this.messageInput.value = '';

                        // Add the sent message immediately to the UI with instant animation
                        if (!this.displayedMessages.has(sentMessage.id)) {
                            await this.addMessageToDOM(sentMessage, true);
                            this.displayedMessages.add(sentMessage.id);
                            this.scrollToBottom();
                        }

                        // Update encryption status back to normal
                        if (this.encryptionReady) {
                            this.updateEncryptionStatus('🔐 End-to-end encryption active', '#4CAF50');
                        }
                    } else {
                        alert('Failed to send message. Please try again.');
                    }
                } catch (error) {
                    console.error('Error sending message:', error);
                    alert('Failed to send message. Please try again.');
                } finally {
                    // Re-enable send button
                    this.sendButton.disabled = false;
                    this.sendButton.textContent = 'Send';
                    this.messageInput.focus();
                }
            }

            scrollToBottom() {
                this.messagesContainer.scrollTo({
                    top: this.messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }

            updateCharacterCount() {
                const remaining = 500 - this.messageInput.value.length;
                this.characterCount.textContent = `${remaining} characters remaining`;

                if (remaining < 50) {
                    this.characterCount.style.color = '#ff6b6b';
                } else {
                    this.characterCount.style.color = '#666';
                }
            }

            updateUsersList() {
                if (this.currentChatType === 'public') {
                    this.usersList.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #666; font-style: italic;">
                            Click "Private Chat" to see online users
                        </div>
                    `;
                    return;
                }

                if (this.onlineUsers.length === 0) {
                    this.usersList.innerHTML = `
                        <div style="text-align: center; padding: 20px; color: #666; font-style: italic;">
                            No other users online
                        </div>
                    `;
                    return;
                }

                let html = '';
                this.onlineUsers.forEach(user => {
                    const isActive = this.currentChatWith === user.username;
                    const avatar = user.username.charAt(0).toUpperCase();

                    html += `
                        <div class="user-item ${isActive ? 'active' : ''}" onclick="chatApp.startPrivateChat('${user.username}')">
                            <div class="user-avatar">${avatar}</div>
                            <div class="user-info">
                                <div class="user-name">${user.username}</div>
                                <div class="user-status">Online</div>
                            </div>
                            <div class="online-dot"></div>
                        </div>
                    `;
                });

                this.usersList.innerHTML = html;
            }

            startPrivateChat(username) {
                this.currentChatType = 'private';
                this.currentChatWith = username;

                // Update UI
                this.updateChatTitle();
                this.updateUsersList();

                // Clear current messages and load private chat
                this.displayedMessages.clear();
                this.messagesContainer.innerHTML = '';
                this.loadMessages();

                // Update placeholder
                this.messageInput.placeholder = `Send a private message to ${username}...`;

                console.log(`🔐 Started private chat with ${username}`);
            }

            updateChatTitle() {
                if (this.currentChatType === 'public') {
                    this.chatTitle.innerHTML = `
                        <span>🌐 Public Chat</span>
                        <span class="chat-type-indicator">Public</span>
                    `;
                } else {
                    this.chatTitle.innerHTML = `
                        <span>🔒 Private Chat with ${this.currentChatWith}</span>
                        <span class="chat-type-indicator">Private</span>
                    `;
                }
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Global functions for UI interaction
        function switchToPublicChat() {
            chatApp.currentChatType = 'public';
            chatApp.currentChatWith = null;

            // Update tabs
            document.getElementById('publicTab').classList.add('active');
            document.getElementById('privateTab').classList.remove('active');

            // Update UI
            chatApp.updateChatTitle();
            chatApp.updateUsersList();

            // Clear current messages and load public chat
            chatApp.displayedMessages.clear();
            chatApp.messagesContainer.innerHTML = '';
            chatApp.loadMessages();

            // Update placeholder
            chatApp.messageInput.placeholder = 'Type your message...';

            console.log('🌐 Switched to public chat');
        }

        function showUsersList() {
            chatApp.currentChatType = 'private';

            // Update tabs
            document.getElementById('publicTab').classList.remove('active');
            document.getElementById('privateTab').classList.add('active');

            // Load and show users
            chatApp.loadOnlineUsers();

            // Clear chat if no specific user selected
            if (!chatApp.currentChatWith) {
                chatApp.displayedMessages.clear();
                chatApp.messagesContainer.innerHTML = `
                    <div class="no-messages">
                        Select a user from the sidebar to start a private chat
                    </div>
                `;
                chatApp.chatTitle.innerHTML = `
                    <span>👥 Private Chat</span>
                    <span class="chat-type-indicator">Select User</span>
                `;
            }

            console.log('👥 Switched to private chat mode');
        }

        // Global variable to access chat app
        let chatApp;

        // Initialize the chat app when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            chatApp = new ChatApp();
        });
    </script>
</body>
</html>
