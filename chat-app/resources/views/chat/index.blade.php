<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title><PERSON><PERSON>t App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
        }

        .chat-container {
            width: 90%;
            max-width: 800px;
            height: 90vh;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .chat-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
            position: relative;
        }

        .chat-header h1 {
            font-size: 24px;
            margin-bottom: 5px;
        }

        .user-info {
            font-size: 14px;
            opacity: 0.9;
        }

        .online-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            width: 12px;
            height: 12px;
            background: #4CAF50;
            border-radius: 50%;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }

        .messages-container {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
            background: #f8f9fa;
        }

        .message {
            margin-bottom: 15px;
            opacity: 0;
            animation: slideIn 0.4s ease-out forwards;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .message.instant {
            animation: fadeIn 0.2s ease-out forwards;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
            }
            to {
                opacity: 1;
            }
        }

        .message.own {
            text-align: right;
        }

        .message-bubble {
            display: inline-block;
            max-width: 70%;
            padding: 12px 16px;
            border-radius: 18px;
            word-wrap: break-word;
        }

        .message.own .message-bubble {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .message.other .message-bubble {
            background: white;
            color: #333;
            border: 1px solid #e0e0e0;
        }

        .message-info {
            font-size: 12px;
            margin-bottom: 5px;
            opacity: 0.7;
        }

        .message.own .message-info {
            text-align: right;
        }

        .message.other .message-info {
            text-align: left;
        }

        .input-container {
            padding: 20px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            gap: 10px;
        }

        .message-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            outline: none;
            font-size: 16px;
            transition: border-color 0.3s;
        }

        .message-input:focus {
            border-color: #667eea;
        }

        .send-button {
            padding: 12px 24px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            transition: transform 0.2s;
        }

        .send-button:hover {
            transform: translateY(-2px);
        }

        .send-button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .typing-indicator {
            padding: 10px 20px;
            font-style: italic;
            color: #666;
            font-size: 14px;
        }

        .no-messages {
            text-align: center;
            color: #666;
            font-style: italic;
            margin-top: 50px;
        }

        /* Responsive design */
        @media (max-width: 768px) {
            .chat-container {
                width: 95%;
                height: 95vh;
                border-radius: 10px;
            }

            .message-bubble {
                max-width: 85%;
            }

            .chat-header h1 {
                font-size: 20px;
            }
        }

        /* Scrollbar styling */
        .messages-container::-webkit-scrollbar {
            width: 6px;
        }

        .messages-container::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .messages-container::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 3px;
        }

        .messages-container::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }
    </style>
</head>
<body>
    <div class="chat-container">
        <div class="chat-header">
            <div class="online-indicator"></div>
            <h1>🔐 Laravel Chat (E2E Encrypted)</h1>
            <div class="user-info">Welcome, <strong>{{ $username }}</strong>!</div>
            <div class="encryption-status" id="encryptionStatus" style="font-size: 12px; margin-top: 5px; opacity: 0.8;">
                🔄 Initializing encryption...
            </div>
        </div>

        <div class="messages-container" id="messagesContainer">
            <div class="no-messages" id="noMessages">
                No messages yet. Start the conversation!
            </div>
        </div>

        <div class="input-container">
            <input
                type="text"
                class="message-input"
                id="messageInput"
                placeholder="Type your message..."
                maxlength="500"
            >
            <button class="send-button" id="sendButton">Send</button>
        </div>
        <div class="character-count" id="characterCount" style="padding: 5px 20px; font-size: 12px; color: #666; text-align: right;">
            500 characters remaining
        </div>
    </div>

    <!-- Load encryption library -->
    <script src="/js/encryption.js"></script>
    <script>
        // Chat application JavaScript
        class ChatApp {
            constructor() {
                this.messagesContainer = document.getElementById('messagesContainer');
                this.messageInput = document.getElementById('messageInput');
                this.sendButton = document.getElementById('sendButton');
                this.noMessages = document.getElementById('noMessages');
                this.characterCount = document.getElementById('characterCount');
                this.encryptionStatus = document.getElementById('encryptionStatus');
                this.currentUsername = '{{ $username }}';
                this.lastMessageId = null;
                this.displayedMessages = new Set();
                this.encryption = null;
                this.encryptionReady = false;

                this.init();
            }

            async init() {
                // Set up CSRF token for AJAX requests
                const token = document.querySelector('meta[name="csrf-token"]').getAttribute('content');

                // Initialize encryption first
                await this.initializeEncryption();

                // Set up event listeners
                this.sendButton.addEventListener('click', () => this.sendMessage());
                this.messageInput.addEventListener('keypress', (e) => {
                    if (e.key === 'Enter') {
                        this.sendMessage();
                    }
                });
                this.messageInput.addEventListener('input', () => this.updateCharacterCount());

                // Load initial messages
                this.loadMessages();

                // Poll for new messages every 1 second for better real-time experience
                setInterval(() => this.loadMessages(), 1000);

                // Initial character count update
                this.updateCharacterCount();
            }

            async initializeEncryption() {
                try {
                    this.updateEncryptionStatus('🔄 Initializing encryption...', '#ffa500');

                    this.encryption = new E2EEncryption();
                    await this.encryption.init();

                    // Test encryption
                    const testResult = await this.encryption.testEncryption();
                    if (testResult) {
                        this.encryptionReady = true;
                        this.updateEncryptionStatus('🔐 End-to-end encryption active', '#4CAF50');
                        console.log('✅ Encryption initialized successfully');
                    } else {
                        throw new Error('Encryption test failed');
                    }
                } catch (error) {
                    console.error('❌ Encryption initialization failed:', error);
                    this.updateEncryptionStatus('⚠️ Encryption failed - messages not secure', '#ff6b6b');
                    this.encryptionReady = false;
                }
            }

            updateEncryptionStatus(message, color = '#666') {
                if (this.encryptionStatus) {
                    this.encryptionStatus.textContent = message;
                    this.encryptionStatus.style.color = color;
                }
            }

            async loadMessages() {
                try {
                    const response = await fetch('/messages');
                    const messages = await response.json();
                    this.processMessages(messages);
                } catch (error) {
                    console.error('Error loading messages:', error);
                }
            }

            async processMessages(messages) {
                if (messages.length === 0) {
                    this.noMessages.style.display = 'block';
                    return;
                }

                this.noMessages.style.display = 'none';

                // If this is the first load, display all messages
                if (this.displayedMessages.size === 0) {
                    for (const message of messages) {
                        await this.addMessageToDOM(message);
                        this.displayedMessages.add(message.id);
                    }
                    this.scrollToBottom();
                    return;
                }

                // Only add new messages that haven't been displayed yet
                let hasNewMessages = false;
                for (const message of messages) {
                    if (!this.displayedMessages.has(message.id)) {
                        await this.addMessageToDOM(message);
                        this.displayedMessages.add(message.id);
                        hasNewMessages = true;
                    }
                }

                // Only scroll to bottom if there are new messages
                if (hasNewMessages) {
                    this.scrollToBottom();
                }
            }

            async addMessageToDOM(message, isInstant = false) {
                const messageDiv = document.createElement('div');
                const isOwnMessage = message.username === this.currentUsername;
                messageDiv.className = `message ${isOwnMessage ? 'own' : 'other'}${isInstant ? ' instant' : ''}`;

                // Decrypt message if it's encrypted and encryption is ready
                let displayMessage = message.message;
                let encryptionIcon = '';

                if (message.encrypted && this.encryptionReady && this.encryption) {
                    try {
                        displayMessage = await this.encryption.decryptMessage(message.message);
                        encryptionIcon = '🔐 ';
                    } catch (error) {
                        console.error('Failed to decrypt message:', error);
                        displayMessage = '[🔒 Encrypted message - decryption failed]';
                        encryptionIcon = '⚠️ ';
                    }
                } else if (message.encrypted) {
                    displayMessage = '[🔒 Encrypted message - encryption not ready]';
                    encryptionIcon = '⚠️ ';
                }

                messageDiv.innerHTML = `
                    <div class="message-info">
                        ${encryptionIcon}${isOwnMessage ? 'You' : message.username} • ${message.formatted_time}
                    </div>
                    <div class="message-bubble">
                        ${this.escapeHtml(displayMessage)}
                    </div>
                `;

                this.messagesContainer.appendChild(messageDiv);
            }

            async sendMessage() {
                const message = this.messageInput.value.trim();
                if (!message) return;

                // Disable send button
                this.sendButton.disabled = true;
                this.sendButton.textContent = 'Encrypting...';

                try {
                    let messageToSend = message;
                    let isEncrypted = false;

                    // Encrypt message if encryption is ready
                    if (this.encryptionReady && this.encryption) {
                        try {
                            messageToSend = await this.encryption.encryptMessage(message);
                            isEncrypted = true;
                            this.sendButton.textContent = 'Sending...';
                        } catch (error) {
                            console.error('Encryption failed:', error);
                            this.updateEncryptionStatus('⚠️ Encryption failed - sending unencrypted', '#ff6b6b');
                            // Fall back to unencrypted
                            messageToSend = message;
                            isEncrypted = false;
                        }
                    } else {
                        console.warn('Encryption not ready - sending unencrypted message');
                        this.updateEncryptionStatus('⚠️ Sending unencrypted message', '#ffa500');
                    }

                    const response = await fetch('/messages', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                        },
                        body: JSON.stringify({
                            message: messageToSend,
                            encrypted: isEncrypted
                        })
                    });

                    if (response.ok) {
                        const sentMessage = await response.json();
                        this.messageInput.value = '';

                        // Add the sent message immediately to the UI with instant animation
                        if (!this.displayedMessages.has(sentMessage.id)) {
                            await this.addMessageToDOM(sentMessage, true);
                            this.displayedMessages.add(sentMessage.id);
                            this.scrollToBottom();
                        }

                        // Update encryption status back to normal
                        if (this.encryptionReady) {
                            this.updateEncryptionStatus('🔐 End-to-end encryption active', '#4CAF50');
                        }
                    } else {
                        alert('Failed to send message. Please try again.');
                    }
                } catch (error) {
                    console.error('Error sending message:', error);
                    alert('Failed to send message. Please try again.');
                } finally {
                    // Re-enable send button
                    this.sendButton.disabled = false;
                    this.sendButton.textContent = 'Send';
                    this.messageInput.focus();
                }
            }

            scrollToBottom() {
                this.messagesContainer.scrollTo({
                    top: this.messagesContainer.scrollHeight,
                    behavior: 'smooth'
                });
            }

            updateCharacterCount() {
                const remaining = 500 - this.messageInput.value.length;
                this.characterCount.textContent = `${remaining} characters remaining`;

                if (remaining < 50) {
                    this.characterCount.style.color = '#ff6b6b';
                } else {
                    this.characterCount.style.color = '#666';
                }
            }

            escapeHtml(text) {
                const div = document.createElement('div');
                div.textContent = text;
                return div.innerHTML;
            }
        }

        // Initialize the chat app when the page loads
        document.addEventListener('DOMContentLoaded', () => {
            new ChatApp();
        });
    </script>
</body>
</html>
